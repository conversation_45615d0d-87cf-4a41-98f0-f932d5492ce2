#[cfg(test)]
mod test_inlines_macro {
    use crate::md_elem::tree_test_utils::inlines;
    use crate::md_elem::tree::elem::{Inline, Span, SpanVariant, Text, TextVariant};

    #[test]
    fn test_empty() {
        let result = inlines![];
        assert_eq!(result, Vec::<Inline>::new());
    }

    #[test]
    fn test_single_string() {
        let result = inlines!["foo"];
        let expected = vec![
            Inline::Text(Text {
                variant: TextVariant::Plain,
                value: "foo".to_string(),
            })
        ];
        assert_eq!(result, expected);
    }

    #[test]
    fn test_multiple_strings() {
        let result = inlines!["foo", "bar"];
        let expected = vec![
            Inline::Text(Text {
                variant: TextVariant::Plain,
                value: "foo".to_string(),
            }),
            Inline::Text(Text {
                variant: TextVariant::Plain,
                value: "bar".to_string(),
            })
        ];
        assert_eq!(result, expected);
    }

    #[test]
    fn test_emphasis() {
        let result = inlines![em{["foo"]}];
        let expected = vec![
            Inline::Span(Span {
                variant: SpanVariant::Emphasis,
                children: vec![
                    Inline::Text(Text {
                        variant: TextVariant::Plain,
                        value: "foo".to_string(),
                    })
                ],
            })
        ];
        assert_eq!(result, expected);
    }

    #[test]
    fn test_strong() {
        let result = inlines![strong{["foo"]}];
        let expected = vec![
            Inline::Span(Span {
                variant: SpanVariant::Strong,
                children: vec![
                    Inline::Text(Text {
                        variant: TextVariant::Plain,
                        value: "foo".to_string(),
                    })
                ],
            })
        ];
        assert_eq!(result, expected);
    }

    #[test]
    fn test_nested() {
        let result = inlines!["foo", em{["bar", strong{["baz"]}]}];
        let expected = vec![
            Inline::Text(Text {
                variant: TextVariant::Plain,
                value: "foo".to_string(),
            }),
            Inline::Span(Span {
                variant: SpanVariant::Emphasis,
                children: vec![
                    Inline::Text(Text {
                        variant: TextVariant::Plain,
                        value: "bar".to_string(),
                    }),
                    Inline::Span(Span {
                        variant: SpanVariant::Strong,
                        children: vec![
                            Inline::Text(Text {
                                variant: TextVariant::Plain,
                                value: "baz".to_string(),
                            })
                        ],
                    })
                ],
            })
        ];
        assert_eq!(result, expected);
    }
}
